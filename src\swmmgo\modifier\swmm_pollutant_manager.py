import os
import shutil
from typing import Dict, List, Any, Optional, Union


class SwmmPollutantManager:
    """
    A class to manage SWMM pollutant sections and add them to SWMM input files.
    
    This class provides a data-driven approach to adding pollutant-related sections
    to SWMM files using structured data dictionaries.
    """
    
    def __init__(self):
        """Initialize the SwmmPollutantManager."""
        self.pollutants_data = None
        self.landuses_data = None
        self.coverages_data = None
        self.loadings_data = None
        self.buildup_data = None
        self.washoff_data = None
    
    def set_pollutants_data(self, pollutants_data: Dict[str, List[Any]]):
        """
        Set the pollutants data.
        
        Args:
            pollutants_data: Dictionary containing pollutant definitions with keys:
                - Name: List of pollutant names
                - Units: List of units (e.g., 'MG/L', 'UG/L')
                - Crain: List of concentrations in rainfall
                - Cgw: List of concentrations in groundwater
                - Crdii: List of concentrations in RDII
                - Kdecay: List of decay coefficients
                - SnowOnly: List of snow-only flags ('YES' or 'NO')
                - Co-Pollutant: List of co-pollutant names (use '*' for none)
                - Co-Frac: List of co-pollutant fractions
                - Cdwf: List of dry weather flow concentrations
                - Cinit: List of initial concentrations
        """
        self.pollutants_data = pollutants_data
    
    def set_landuses_data(self, landuses_data: Dict[str, List[Any]]):
        """
        Set the land uses data.
        
        Args:
            landuses_data: Dictionary containing land use definitions with keys:
                - Name: List of land use names
                - Sweeping_Interval: List of sweeping intervals
                - Fraction_Available: List of fractions available for sweeping
                - Last_Swept: List of last swept values
        """
        self.landuses_data = landuses_data
    
    def set_coverages_data(self, coverages_data: Dict[str, List[Any]]):
        """
        Set the coverages data (assignment of land uses to subcatchments).
        
        Args:
            coverages_data: Dictionary containing coverage definitions with keys:
                - Subcatchment: List of subcatchment IDs
                - Land_Use: List of land use names
                - Percent: List of percentages
        """
        self.coverages_data = coverages_data
    
    def set_loadings_data(self, loadings_data: Dict[str, List[Any]]):
        """
        Set the loadings data (initial pollutant loads on subcatchments).
        
        Args:
            loadings_data: Dictionary containing loading definitions with keys:
                - Subcatchment: List of subcatchment IDs
                - Pollutant: List of pollutant names
                - Buildup: List of initial buildup values
        """
        self.loadings_data = loadings_data
    
    def set_buildup_data(self, buildup_data: Dict[str, List[Any]]):
        """
        Set the buildup data (buildup functions for pollutants and land uses).
        
        Args:
            buildup_data: Dictionary containing buildup function definitions with keys:
                - Land_Use: List of land use names
                - Pollutant: List of pollutant names
                - Function: List of function types ('POW', 'EXP', 'SAT', 'EXT')
                - Coeff1: List of coefficient 1 values
                - Coeff2: List of coefficient 2 values
                - Coeff3: List of coefficient 3 values
                - Per_Unit: List of per unit types ('AREA', 'CURB')
        """
        self.buildup_data = buildup_data
    
    def set_washoff_data(self, washoff_data: Dict[str, List[Any]]):
        """
        Set the washoff data (washoff functions for pollutants and land uses).
        
        Args:
            washoff_data: Dictionary containing washoff function definitions with keys:
                - Land_Use: List of land use names
                - Pollutant: List of pollutant names
                - Function: List of function types ('EXP', 'RC', 'EMC')
                - Coeff1: List of coefficient 1 values
                - Coeff2: List of coefficient 2 values
                - SweepRmvl: List of sweeping removal efficiencies
                - BmpRmvl: List of best management practice (BMP) removal efficiencies
        """
        self.washoff_data = washoff_data
    
    def set_all_data(self, pollutants_data: Dict[str, List[Any]], 
                     landuses_data: Dict[str, List[Any]],
                     coverages_data: Dict[str, List[Any]], 
                     loadings_data: Dict[str, List[Any]],
                     buildup_data: Dict[str, List[Any]], 
                     washoff_data: Dict[str, List[Any]]):
        """
        Set all pollutant-related data at once.
        
        Args:
            pollutants_data: Pollutant definitions
            landuses_data: Land use definitions
            coverages_data: Coverage definitions
            loadings_data: Loading definitions
            buildup_data: Buildup function definitions
            washoff_data: Washoff function definitions
        """
        self.set_pollutants_data(pollutants_data)
        self.set_landuses_data(landuses_data)
        self.set_coverages_data(coverages_data)
        self.set_loadings_data(loadings_data)
        self.set_buildup_data(buildup_data)
        self.set_washoff_data(washoff_data)
    
    def validate_data(self) -> List[str]:
        """
        Validate that all required data is set and consistent.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        if not self.pollutants_data:
            errors.append("Pollutants data is not set")
        if not self.landuses_data:
            errors.append("Land uses data is not set")
        if not self.coverages_data:
            errors.append("Coverages data is not set")
        if not self.loadings_data:
            errors.append("Loadings data is not set")
        if not self.buildup_data:
            errors.append("Buildup data is not set")
        if not self.washoff_data:
            errors.append("Washoff data is not set")
        
        # Additional validation can be added here
        # e.g., check that pollutant names in loadings match those in pollutants_data
        
        return errors

    # ========== BUILDER-STYLE INTERFACE FOR MODULAR CONFIGURATION ==========

    def add_pollutant(self, name: str, units: str = 'MG/L', crain: float = 0.0,
                      cgw: float = 0.0, crdii: int = 0, kdecay: float = 0.0,
                      snow_only: str = 'NO', co_pollutant: str = '*',
                      co_frac: float = 0.0, cdwf: int = 0, cinit: int = 0):
        """
        Add a single pollutant to the configuration.

        Args:
            name: Pollutant name
            units: Units (default: 'MG/L')
            crain: Concentration in rainfall (default: 0.0)
            cgw: Concentration in groundwater (default: 0.0)
            crdii: Concentration in RDII (default: 0)
            kdecay: Decay coefficient (default: 0.0)
            snow_only: Snow-only flag 'YES' or 'NO' (default: 'NO')
            co_pollutant: Co-pollutant name or '*' for none (default: '*')
            co_frac: Co-pollutant fraction (default: 0.0)
            cdwf: Dry weather flow concentration (default: 0)
            cinit: Initial concentration (default: 0)
        """
        if self.pollutants_data is None:
            self.pollutants_data = {
                'Name': [], 'Units': [], 'Crain': [], 'Cgw': [], 'Crdii': [],
                'Kdecay': [], 'SnowOnly': [], 'Co-Pollutant': [], 'Co-Frac': [],
                'Cdwf': [], 'Cinit': []
            }

        self.pollutants_data['Name'].append(name)
        self.pollutants_data['Units'].append(units)
        self.pollutants_data['Crain'].append(crain)
        self.pollutants_data['Cgw'].append(cgw)
        self.pollutants_data['Crdii'].append(crdii)
        self.pollutants_data['Kdecay'].append(kdecay)
        self.pollutants_data['SnowOnly'].append(snow_only)
        self.pollutants_data['Co-Pollutant'].append(co_pollutant)
        self.pollutants_data['Co-Frac'].append(co_frac)
        self.pollutants_data['Cdwf'].append(cdwf)
        self.pollutants_data['Cinit'].append(cinit)

        return self  # For method chaining

    def add_land_use(self, name: str, sweeping_interval: int = 0,
                     fraction_available: float = 0.0, last_swept: int = 0):
        """
        Add a single land use to the configuration.

        Args:
            name: Land use name
            sweeping_interval: Sweeping interval in days (default: 0)
            fraction_available: Fraction available for sweeping (default: 0.0)
            last_swept: Days since last swept (default: 0)
        """
        if self.landuses_data is None:
            self.landuses_data = {
                'Name': [], 'Sweeping_Interval': [], 'Fraction_Available': [], 'Last_Swept': []
            }

        self.landuses_data['Name'].append(name)
        self.landuses_data['Sweeping_Interval'].append(sweeping_interval)
        self.landuses_data['Fraction_Available'].append(fraction_available)
        self.landuses_data['Last_Swept'].append(last_swept)

        return self  # For method chaining

    def assign_land_use_to_subcatchment(self, subcatchment_id: str, land_use: str, percent: float):
        """
        Assign a land use to a subcatchment with a specific percentage.

        Args:
            subcatchment_id: Subcatchment ID
            land_use: Land use name
            percent: Percentage of subcatchment covered by this land use
        """
        if self.coverages_data is None:
            self.coverages_data = {
                'Subcatchment': [], 'Land_Use': [], 'Percent': []
            }

        self.coverages_data['Subcatchment'].append(subcatchment_id)
        self.coverages_data['Land_Use'].append(land_use)
        self.coverages_data['Percent'].append(percent)

        return self  # For method chaining

    def set_initial_pollutant_loading(self, subcatchment_id: str, pollutant: str, buildup: float):
        """
        Set initial pollutant loading for a subcatchment.

        Args:
            subcatchment_id: Subcatchment ID
            pollutant: Pollutant name
            buildup: Initial buildup value
        """
        if self.loadings_data is None:
            self.loadings_data = {
                'Subcatchment': [], 'Pollutant': [], 'Buildup': []
            }

        self.loadings_data['Subcatchment'].append(subcatchment_id)
        self.loadings_data['Pollutant'].append(pollutant)
        self.loadings_data['Buildup'].append(buildup)

        return self  # For method chaining

    def add_buildup_function(self, land_use: str, pollutant: str, function: str = 'POW',
                            coeff1: float = 1.0, coeff2: float = 0.6, coeff3: float = 0.0,
                            per_unit: str = 'AREA'):
        """
        Add a buildup function for a land use and pollutant combination.

        Args:
            land_use: Land use name
            pollutant: Pollutant name
            function: Function type ('POW', 'EXP', 'SAT', 'EXT') (default: 'POW')
            coeff1: Coefficient 1 (default: 1.0)
            coeff2: Coefficient 2 (default: 0.6)
            coeff3: Coefficient 3 (default: 0.0)
            per_unit: Per unit type ('AREA', 'CURB') (default: 'AREA')
        """
        if self.buildup_data is None:
            self.buildup_data = {
                'Land_Use': [], 'Pollutant': [], 'Function': [], 'Coeff1': [],
                'Coeff2': [], 'Coeff3': [], 'Per_Unit': []
            }

        self.buildup_data['Land_Use'].append(land_use)
        self.buildup_data['Pollutant'].append(pollutant)
        self.buildup_data['Function'].append(function)
        self.buildup_data['Coeff1'].append(coeff1)
        self.buildup_data['Coeff2'].append(coeff2)
        self.buildup_data['Coeff3'].append(coeff3)
        self.buildup_data['Per_Unit'].append(per_unit)

        return self  # For method chaining

    def add_washoff_function(self, land_use: str, pollutant: str, function: str = 'EXP',
                            coeff1: float = 0.01, coeff2: float = 1.5,
                            sweep_rmvl: float = 0.0, bmp_rmvl: float = 0.0):
        """
        Add a washoff function for a land use and pollutant combination.

        Args:
            land_use: Land use name
            pollutant: Pollutant name
            function: Function type ('EXP', 'RC', 'EMC') (default: 'EXP')
            coeff1: Coefficient 1 (default: 0.01)
            coeff2: Coefficient 2 (default: 1.5)
            sweep_rmvl: Sweeping removal efficiency (default: 0.0)
            bmp_rmvl: BMP removal efficiency (default: 0.0)
        """
        if self.washoff_data is None:
            self.washoff_data = {
                'Land_Use': [], 'Pollutant': [], 'Function': [], 'Coeff1': [],
                'Coeff2': [], 'SweepRmvl': [], 'BmpRmvl': []
            }

        self.washoff_data['Land_Use'].append(land_use)
        self.washoff_data['Pollutant'].append(pollutant)
        self.washoff_data['Function'].append(function)
        self.washoff_data['Coeff1'].append(coeff1)
        self.washoff_data['Coeff2'].append(coeff2)
        self.washoff_data['SweepRmvl'].append(sweep_rmvl)
        self.washoff_data['BmpRmvl'].append(bmp_rmvl)

        return self  # For method chaining

    # ========== CONVENIENCE METHODS FOR COMMON SCENARIOS ==========

    def configure_subcatchment_pollutants(self, subcatchment_ids: List[str],
                                         land_use_assignments: Dict[str, float],
                                         pollutants: List[str],
                                         initial_loadings: Optional[Dict[str, float]] = None):
        """
        Configure multiple subcatchments with land uses and pollutants in one call.

        Args:
            subcatchment_ids: List of subcatchment IDs to configure
            land_use_assignments: Dict mapping land use names to percentages
            pollutants: List of pollutant names to apply
            initial_loadings: Optional dict mapping pollutant names to initial loading values
        """
        for subcatchment_id in subcatchment_ids:
            # Assign land uses to subcatchment
            for land_use, percent in land_use_assignments.items():
                self.assign_land_use_to_subcatchment(subcatchment_id, land_use, percent)

            # Set initial pollutant loadings
            for pollutant in pollutants:
                loading_value = 0.0
                if initial_loadings and pollutant in initial_loadings:
                    loading_value = initial_loadings[pollutant]
                self.set_initial_pollutant_loading(subcatchment_id, pollutant, loading_value)

        return self  # For method chaining

    def add_pollutant_land_use_combination(self, pollutant_name: str, land_use_name: str,
                                          pollutant_params: Optional[Dict[str, Any]] = None,
                                          land_use_params: Optional[Dict[str, Any]] = None,
                                          buildup_params: Optional[Dict[str, Any]] = None,
                                          washoff_params: Optional[Dict[str, Any]] = None):
        """
        Add a complete pollutant-land use combination with all associated parameters.

        Args:
            pollutant_name: Name of the pollutant
            land_use_name: Name of the land use
            pollutant_params: Optional pollutant parameters (uses defaults if not provided)
            land_use_params: Optional land use parameters (uses defaults if not provided)
            buildup_params: Optional buildup function parameters (uses defaults if not provided)
            washoff_params: Optional washoff function parameters (uses defaults if not provided)
        """
        # Add pollutant if not already added
        if self.pollutants_data is None or pollutant_name not in self.pollutants_data.get('Name', []):
            params = pollutant_params or {}
            self.add_pollutant(pollutant_name, **params)

        # Add land use if not already added
        if self.landuses_data is None or land_use_name not in self.landuses_data.get('Name', []):
            params = land_use_params or {}
            self.add_land_use(land_use_name, **params)

        # Add buildup function
        buildup_params = buildup_params or {}
        self.add_buildup_function(land_use_name, pollutant_name, **buildup_params)

        # Add washoff function
        washoff_params = washoff_params or {}
        self.add_washoff_function(land_use_name, pollutant_name, **washoff_params)

        return self  # For method chaining

    def add_pollutant_sections_to_file(self, swmm_file_path: str,
                                       output_file_path: Optional[str] = None) -> str:
        """
        Add pollutant sections to a SWMM input file.

        Args:
            swmm_file_path: Path to the input SWMM file
            output_file_path: Path for the output file (if None, modifies input file)

        Returns:
            Path to the modified SWMM file

        Raises:
            ValueError: If data validation fails
            FileNotFoundError: If input file doesn't exist
        """
        # Validate data first
        validation_errors = self.validate_data()
        if validation_errors:
            raise ValueError(f"Data validation failed: {', '.join(validation_errors)}")

        if not os.path.exists(swmm_file_path):
            raise FileNotFoundError(f"SWMM file not found: {swmm_file_path}")

        # Determine output file path
        if output_file_path is None:
            output_file_path = swmm_file_path
        else:
            # Copy input file to output location if different
            if swmm_file_path != output_file_path:
                shutil.copy2(swmm_file_path, output_file_path)

        # Read the file
        with open(output_file_path, 'r') as f:
            lines = f.readlines()

        # Generate pollutant sections
        pollutant_sections = self._generate_pollutant_sections()

        # Insert sections into the file
        modified_lines = self._insert_sections_into_file(lines, pollutant_sections)

        # Write the modified file
        with open(output_file_path, 'w') as f:
            f.writelines(modified_lines)

        return output_file_path

    def _generate_pollutant_sections(self) -> List[str]:
        """
        Generate all pollutant-related sections as a list of strings.

        Returns:
            List of strings representing the pollutant sections
        """
        sections = []

        # Add POLLUTANTS section
        sections.extend(self._generate_pollutants_section())

        # Add LANDUSES section
        sections.extend(self._generate_landuses_section())

        # Add COVERAGES section
        sections.extend(self._generate_coverages_section())

        # Add LOADINGS section
        sections.extend(self._generate_loadings_section())

        # Add BUILDUP section
        sections.extend(self._generate_buildup_section())

        # Add WASHOFF section
        sections.extend(self._generate_washoff_section())

        return sections

    def _generate_pollutants_section(self) -> List[str]:
        """Generate the [POLLUTANTS] section."""
        section = []
        section.append('[POLLUTANTS]\n')
        section.append(';;Name           Units  Crain      Cgw        Crdii      Kdecay     SnowOnly   Co-Pollutant     Co-Frac    Cdwf       Cinit     \n')
        section.append(';;-------------- ------ ---------- ---------- ---------- ---------- ---------- ---------------- ---------- ---------- ----------\n')

        for i in range(len(self.pollutants_data['Name'])):
            name = self.pollutants_data['Name'][i]
            units = self.pollutants_data['Units'][i]
            crain = self.pollutants_data['Crain'][i]
            cgw = self.pollutants_data['Cgw'][i]
            crdii = self.pollutants_data['Crdii'][i]
            kdecay = self.pollutants_data['Kdecay'][i]
            snow_only = self.pollutants_data['SnowOnly'][i]
            co_pollutant = self.pollutants_data['Co-Pollutant'][i]
            co_frac = self.pollutants_data['Co-Frac'][i]
            cdwf = self.pollutants_data['Cdwf'][i]
            cinit = self.pollutants_data['Cinit'][i]

            line = f'{name:<16} {units:<6} {crain:<10} {cgw:<10} {crdii:<10} {kdecay:<10} {snow_only:<10} {co_pollutant:<16} {co_frac:<10} {cdwf:<10} {cinit}\n'
            section.append(line)

        section.append('\n')
        return section

    def _generate_landuses_section(self) -> List[str]:
        """Generate the [LANDUSES] section."""
        section = []
        section.append('[LANDUSES]\n')
        section.append(';;               Sweeping   Fraction   Last      \n')
        section.append(';;Name           Interval   Available  Swept      \n')
        section.append(';;-------------- ---------- ---------- ----------\n')

        for i in range(len(self.landuses_data['Name'])):
            name = self.landuses_data['Name'][i]
            interval = self.landuses_data['Sweeping_Interval'][i]
            available = self.landuses_data['Fraction_Available'][i]
            swept = self.landuses_data['Last_Swept'][i]

            section.append(f'{name:<16} {interval:<10} {available:<10} {swept:<10}\n')

        section.append('\n')
        return section

    def _generate_coverages_section(self) -> List[str]:
        """Generate the [COVERAGES] section."""
        section = []
        section.append('[COVERAGES]\n')
        section.append(';;Subcatchment   Land Use        Percent\n')
        section.append(';;-------------- --------------- -------\n')

        for i in range(len(self.coverages_data['Subcatchment'])):
            subcatchment = self.coverages_data['Subcatchment'][i]
            land_use = self.coverages_data['Land_Use'][i]
            percent = self.coverages_data['Percent'][i]

            section.append(f'{subcatchment:<16} {land_use:<15} {percent}\n')

        section.append('\n')
        return section

    def _generate_loadings_section(self) -> List[str]:
        """Generate the [LOADINGS] section."""
        section = []
        section.append('[LOADINGS]\n')
        section.append(';;Subcatchment   Pollutant        Buildup\n')
        section.append(';;-------------- ---------------- ----------\n')

        for i in range(len(self.loadings_data['Subcatchment'])):
            subcatchment = self.loadings_data['Subcatchment'][i]
            pollutant = self.loadings_data['Pollutant'][i]
            buildup = self.loadings_data['Buildup'][i]

            section.append(f'{subcatchment:<16} {pollutant:<16} {buildup}\n')

        section.append('\n')
        return section

    def _generate_buildup_section(self) -> List[str]:
        """Generate the [BUILDUP] section."""
        section = []
        section.append('[BUILDUP]\n')
        section.append(';;Land Use       Pollutant        Function   Coeff1     Coeff2     Coeff3     Per Unit  \n')
        section.append(';;-------------- ---------------- ---------- ---------- ---------- ---------- ----------\n')

        for i in range(len(self.buildup_data['Land_Use'])):
            land_use = self.buildup_data['Land_Use'][i]
            pollutant = self.buildup_data['Pollutant'][i]
            function = self.buildup_data['Function'][i]
            coeff1 = self.buildup_data['Coeff1'][i]
            coeff2 = self.buildup_data['Coeff2'][i]
            coeff3 = self.buildup_data['Coeff3'][i]
            per_unit = self.buildup_data['Per_Unit'][i]

            section.append(f'{land_use:<16} {pollutant:<16} {function:<10} {coeff1:<10} {coeff2:<10} {coeff3:<10} {per_unit:<10}\n')

        section.append('\n')
        return section

    def _generate_washoff_section(self) -> List[str]:
        """Generate the [WASHOFF] section."""
        section = []
        section.append('[WASHOFF]\n')
        section.append(';;Land Use       Pollutant        Function   Coeff1     Coeff2     SweepRmvl  BmpRmvl \n')
        section.append(';;-------------- ---------------- ---------- ---------- ---------- ---------- ----------\n')

        for i in range(len(self.washoff_data['Land_Use'])):
            land_use = self.washoff_data['Land_Use'][i]
            pollutant = self.washoff_data['Pollutant'][i]
            function = self.washoff_data['Function'][i]
            coeff1 = self.washoff_data['Coeff1'][i]
            coeff2 = self.washoff_data['Coeff2'][i]
            sweep_rmvl = self.washoff_data['SweepRmvl'][i]
            bmp_rmvl = self.washoff_data['BmpRmvl'][i]

            section.append(f'{land_use:<16} {pollutant:<16} {function:<10} {coeff1:<10} {coeff2:<10} {sweep_rmvl:<10} {bmp_rmvl:<10}\n')

        section.append('\n')
        return section

    def _insert_sections_into_file(self, lines: List[str], pollutant_sections: List[str]) -> List[str]:
        """
        Insert pollutant sections into the SWMM file lines and update REPORT section.

        Args:
            lines: Original file lines
            pollutant_sections: Pollutant sections to insert

        Returns:
            Modified file lines
        """
        # First, update the REPORT section to include pollutants
        lines = self._update_report_section(lines)

        # Find the [XSECTIONS] section and insert pollutant sections after it
        xsection_start = -1
        xsection_end = -1

        # Find the start of [XSECTIONS]
        for i, line in enumerate(lines):
            if line.strip() == '[XSECTIONS]':
                xsection_start = i
                break

        # If [XSECTIONS] section exists, find its end
        if xsection_start != -1:
            # Find the end of [XSECTIONS] section (next section or end of file)
            for i in range(xsection_start + 1, len(lines)):
                if i + 1 < len(lines) and lines[i].strip() == '' and lines[i + 1].strip().startswith('['):
                    xsection_end = i
                    break

            # If we didn't find the end, it might be the last section
            if xsection_end == -1:
                xsection_end = len(lines) - 1

            # Insert pollutant sections after [XSECTIONS]
            modified_lines = lines[:xsection_end + 1] + pollutant_sections + lines[xsection_end + 1:]
        else:
            # If [XSECTIONS] not found, just append pollutant sections at the end
            modified_lines = lines + pollutant_sections

        return modified_lines

    def _update_report_section(self, lines: List[str]) -> List[str]:
        """
        Update the [REPORT] section to include pollutant reporting.

        Args:
            lines: Original file lines

        Returns:
            Modified file lines with updated REPORT section
        """
        report_start = -1
        report_end = -1

        # Find the [REPORT] section
        for i, line in enumerate(lines):
            if line.strip() == '[REPORT]':
                report_start = i
                break

        if report_start != -1:
            # Find the end of [REPORT] section
            for i in range(report_start + 1, len(lines)):
                if lines[i].strip().startswith('[') and lines[i].strip() != '[REPORT]':
                    report_end = i
                    break

            # If we didn't find the end, it might be the last section
            if report_end == -1:
                report_end = len(lines)

            # Check if POLLUTANTS is already in the report section
            pollutants_already_reported = False
            for i in range(report_start, report_end):
                if 'POLLUTANTS' in lines[i].upper():
                    pollutants_already_reported = True
                    break

            if not pollutants_already_reported:
                # Add POLLUTANTS ALL to the report section
                # Insert before the end of the section
                insert_pos = report_end
                # Look for a good place to insert (after other report items)
                for i in range(report_start + 1, report_end):
                    if lines[i].strip() == '' or lines[i].strip().startswith(';;'):
                        continue
                    elif any(keyword in lines[i].upper() for keyword in ['SUBCATCHMENTS', 'NODES', 'LINKS']):
                        insert_pos = i + 1

                # Insert the POLLUTANTS line with consistent spacing
                new_line = 'POLLUTANTS ALL\n'
                lines.insert(insert_pos, new_line)
                print(f"Added 'POLLUTANTS ALL' to REPORT section at line {insert_pos + 1}")
            else:
                print("POLLUTANTS already found in REPORT section")
        else:
            # No [REPORT] section found, create one
            print("No [REPORT] section found, creating one...")

            # Find a good place to insert the REPORT section (typically near the end, before [TAGS] or [MAP])
            insert_pos = len(lines)
            for i, line in enumerate(lines):
                if line.strip() in ['[TAGS]', '[MAP]', '[COORDINATES]', '[VERTICES]']:
                    insert_pos = i
                    break

            # Create the REPORT section with consistent spacing
            report_section = [
                '\n',
                '[REPORT]\n',
                ';;Reporting Options\n',
                'SUBCATCHMENTS ALL\n',
                'NODES ALL\n',
                'LINKS ALL\n',
                'POLLUTANTS ALL\n',
                '\n'
            ]

            # Insert the new REPORT section
            for j, report_line in enumerate(report_section):
                lines.insert(insert_pos + j, report_line)

            print(f"Created new [REPORT] section with POLLUTANTS ALL at line {insert_pos + 1}")

        return lines
