import os
import unittest
import shutil
import pandas as pd
import numpy as np
import swmmio
from pyswmm import Simulation, Output
from swmmio.utils.modify_model import replace_inp_section

from tests.test_helper import TestHelper
from tests.data import TESTS_DATA_PATH, TESTS_OUTPUT_PATH
from src.swmmgo.modifier.swmm_pollutant_manager import SwmmPollutantManager
from src.swmmgo.modifier.pollutant_configurations import PollutantConfigurations
from src.swmmgo.modifier.modular_pollutant_builder import ModularPollutantBuilder

class TestSwmmPollutants(unittest.TestCase):

    def setUp(self):
        self.output_folder = os.path.join(TESTS_OUTPUT_PATH, 'swmm_pollutants')
        os.makedirs(self.output_folder, exist_ok=True)
        self.helper = TestHelper()

    def test_add_road_pollutants(self):
        """Test adding road pollutants (Zinc and Oil) using the pollutant manager."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'road_pollutants_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Get road pollutants configuration
        config = PollutantConfigurations.get_road_pollutants_zinc_oil()

        # Create and configure the pollutant manager
        pollutant_manager = SwmmPollutantManager()
        pollutant_manager.set_all_data(
            config['pollutants_data'],
            config['landuses_data'],
            config['coverages_data'],
            config['loadings_data'],
            config['buildup_data'],
            config['washoff_data']
        )

        # Add pollutant sections to the SWMM file
        pollutant_manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, config['pollutants_data']['Name'])

    def test_add_urban_pollutants_comprehensive(self):
        """Test adding comprehensive urban pollutants using the pollutant manager."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'urban_pollutants_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Get urban pollutants configuration
        config = PollutantConfigurations.get_urban_pollutants_comprehensive()

        # Create and configure the pollutant manager
        pollutant_manager = SwmmPollutantManager()
        pollutant_manager.set_all_data(
            config['pollutants_data'],
            config['landuses_data'],
            config['coverages_data'],
            config['loadings_data'],
            config['buildup_data'],
            config['washoff_data']
        )

        # Add pollutant sections to the SWMM file
        pollutant_manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, config['pollutants_data']['Name'])

    def test_add_simple_tss_pollutant(self):
        """Test adding simple TSS pollutant using the pollutant manager."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'tss_pollutants_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Get simple TSS configuration
        config = PollutantConfigurations.get_simple_tss_configuration()

        # Create and configure the pollutant manager
        pollutant_manager = SwmmPollutantManager()
        pollutant_manager.set_all_data(
            config['pollutants_data'],
            config['landuses_data'],
            config['coverages_data'],
            config['loadings_data'],
            config['buildup_data'],
            config['washoff_data']
        )

        # Add pollutant sections to the SWMM file
        pollutant_manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, config['pollutants_data']['Name'])

    def test_modular_road_scenario(self):
        """Test modular road scenario builder with custom subcatchments."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'modular_road_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create road scenario for specific subcatchments with custom coverage
        subcatchments = ['S1']  # Adjust based on your model
        road_coverage = 50.0    # 50% road coverage
        pollutants = ['Zinc', 'Oil', 'Lead']  # Custom pollutant list

        manager = ModularPollutantBuilder.create_road_scenario(
            subcatchments, road_coverage, pollutants
        )

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, pollutants)

    def test_modular_urban_scenario(self):
        """Test modular urban scenario builder with custom land use mix."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'modular_urban_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create urban scenario with custom land use mix
        subcatchments = ['S1']
        land_use_mix = {
            'Residential': 70.0,
            'Commercial': 20.0,
            'Industrial': 10.0
        }
        pollutants = ['TSS', 'COD']  # Simplified pollutant list

        manager = ModularPollutantBuilder.create_urban_scenario(
            subcatchments, land_use_mix, pollutants
        )

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, pollutants)

    def test_modular_custom_scenario(self):
        """Test modular custom scenario builder with detailed per-subcatchment configuration."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'modular_custom_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create custom scenario with detailed configuration
        subcatchment_configs = [
            {
                'subcatchment_id': 'S1',
                'land_uses': {'Residential': 60.0, 'Road': 40.0},
                'pollutants': ['TSS', 'Zinc'],
                'initial_loadings': {'TSS': 150, 'Zinc': 80}
            }
        ]

        manager = ModularPollutantBuilder.create_custom_scenario(subcatchment_configs)

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, ['TSS', 'Zinc'])

    def test_builder_style_configuration(self):
        """Test the builder-style configuration interface."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'builder_style_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create configuration using builder-style interface
        manager = (SwmmPollutantManager()
                  .add_pollutant('TSS', units='MG/L', kdecay=0.0)
                  .add_pollutant('Copper', units='UG/L', kdecay=0.02)
                  .add_land_use('Mixed_Urban', sweeping_interval=5, fraction_available=0.2)
                  .assign_land_use_to_subcatchment('S1', 'Mixed_Urban', 100.0)
                  .set_initial_pollutant_loading('S1', 'TSS', 120)
                  .set_initial_pollutant_loading('S1', 'Copper', 50)
                  .add_buildup_function('Mixed_Urban', 'TSS', 'POW', 1.5, 0.7)
                  .add_buildup_function('Mixed_Urban', 'Copper', 'POW', 0.8, 0.5)
                  .add_washoff_function('Mixed_Urban', 'TSS', 'EXP', 0.02, 1.6, 0.6, 0.8)
                  .add_washoff_function('Mixed_Urban', 'Copper', 'EXP', 0.015, 1.4, 0.4, 0.6))

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, ['TSS', 'Copper'])

    def _run_simulation_and_analyze(self, test_swmm_file: str, pollutant_names: list):
        """
        Helper method to run simulation and analyze pollutant results.

        Args:
            test_swmm_file: Path to the SWMM file
            pollutant_names: List of pollutant names to analyze
        """
        # Run the simulation
        output_file = os.path.splitext(test_swmm_file)[0] + '.out'

        # Change to output directory to run simulation
        original_dir = os.getcwd()
        os.chdir(self.output_folder)

        try:
            with Simulation(os.path.basename(test_swmm_file)) as sim:
                for _ in sim:
                    pass

            # Check if output file was created
            self.assertTrue(os.path.exists(output_file), "Output file was not created")

            # Load model to get outfall information
            model = swmmio.Model(test_swmm_file)

            # Analyze results using pyswmm Output
            with Output(output_file) as out:
                # Get outfall nodes
                outfalls = [node for node in model.inp.outfalls.index]

                for outfall in outfalls:
                    print(f"Pollutant concentrations at outfall {outfall}:")

                    # Get pollutant results for this node
                    for pollutant in pollutant_names:
                        try:
                            # Get pollutant concentration time series
                            # out.pollutants is a dictionary, so we need to find the index differently
                            if hasattr(out, 'pollutants'):
                                if isinstance(out.pollutants, dict):
                                    # If pollutants is a dict, get the keys as a list
                                    pollutant_list = list(out.pollutants.keys())
                                elif isinstance(out.pollutants, list):
                                    pollutant_list = out.pollutants
                                else:
                                    # Try to convert to list
                                    pollutant_list = list(out.pollutants)

                                if pollutant in pollutant_list:
                                    pollutant_index = pollutant_list.index(pollutant)
                                else:
                                    print(f"  {pollutant}: Not found in output file pollutants: {pollutant_list}")
                                    continue
                            else:
                                print(f"  {pollutant}: No pollutants attribute in output file")
                                continue

                            conc_series = []

                            for time_idx in range(out.num_periods):
                                conc = out.node_pollutant_quality(outfall, pollutant_index, time_idx)
                                conc_series.append(conc)

                            # Convert to numpy array for calculations
                            conc_array = np.array(conc_series)

                            # Print results
                            print(f"  {pollutant}: Max={np.max(conc_array):.3f}, Avg={np.mean(conc_array):.3f}")

                            # Assert that concentrations are within expected ranges
                            self.assertGreaterEqual(np.max(conc_array), 0,
                                                  f"Maximum {pollutant} concentration should be non-negative")
                        except Exception as e:
                            print(f"Error getting data for {pollutant}: {str(e)}")
                            # Print debug information
                            if hasattr(out, 'pollutants'):
                                print(f"  Debug: out.pollutants type: {type(out.pollutants)}")
                                print(f"  Debug: out.pollutants content: {out.pollutants}")
                            else:
                                print(f"  Debug: No pollutants attribute found")

        finally:
            # Change back to original directory
            os.chdir(original_dir)

if __name__ == '__main__':
    unittest.main()
