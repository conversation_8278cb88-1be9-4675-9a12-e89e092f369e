import os
import unittest
import shutil
import pandas as pd
import numpy as np
import swmmio
from pyswmm import Simulation, Output
from swmmio.utils.modify_model import replace_inp_section

from tests.test_helper import TestHelper
from tests.data import TESTS_DATA_PATH, TESTS_OUTPUT_PATH
from src.swmmgo.modifier.swmm_pollutant_manager import SwmmPollutantManager
from src.swmmgo.modifier.pollutant_configurations import PollutantConfigurations
from src.swmmgo.modifier.modular_pollutant_builder import ModularPollutantBuilder

class TestSwmmPollutants(unittest.TestCase):

    def setUp(self):
        self.output_folder = os.path.join(TESTS_OUTPUT_PATH, 'swmm_pollutants')
        os.makedirs(self.output_folder, exist_ok=True)
        self.helper = TestHelper()

    def test_add_road_pollutants(self):
        """Test adding road pollutants (Zinc and Oil) using the pollutant manager."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'road_pollutants_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Get road pollutants configuration
        config = PollutantConfigurations.get_road_pollutants_zinc_oil()

        # Create and configure the pollutant manager
        pollutant_manager = SwmmPollutantManager()
        pollutant_manager.set_all_data(
            config['pollutants_data'],
            config['landuses_data'],
            config['coverages_data'],
            config['loadings_data'],
            config['buildup_data'],
            config['washoff_data']
        )

        # Add pollutant sections to the SWMM file
        pollutant_manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation first
        self._run_swmm_simulation(test_swmm_file)

        # Get the output file path
        output_file = os.path.splitext(test_swmm_file)[0] + '.out'

        # Then compute outfall loads
        total_kg_zinc = self._compute_outfall_load(output_file, 'O2', 'Zinc')
        total_kg_oil = self._compute_outfall_load(output_file, 'O2', 'Oil')
        print('The total amount of Zinc at O2 is {} kg.'.format(total_kg_zinc))
        print('The total amount of Oil at O2 is {} kg.'.format(total_kg_oil))

    def test_add_urban_pollutants_comprehensive(self):
        """Test adding comprehensive urban pollutants using the pollutant manager."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'urban_pollutants_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Get urban pollutants configuration
        config = PollutantConfigurations.get_urban_pollutants_comprehensive()

        # Create and configure the pollutant manager
        pollutant_manager = SwmmPollutantManager()
        pollutant_manager.set_all_data(
            config['pollutants_data'],
            config['landuses_data'],
            config['coverages_data'],
            config['loadings_data'],
            config['buildup_data'],
            config['washoff_data']
        )

        # Add pollutant sections to the SWMM file
        pollutant_manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, config['pollutants_data']['Name'])

    def test_add_simple_tss_pollutant(self):
        """Test adding simple TSS pollutant using the pollutant manager."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'tss_pollutants_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Get simple TSS configuration
        config = PollutantConfigurations.get_simple_tss_configuration()

        # Create and configure the pollutant manager
        pollutant_manager = SwmmPollutantManager()
        pollutant_manager.set_all_data(
            config['pollutants_data'],
            config['landuses_data'],
            config['coverages_data'],
            config['loadings_data'],
            config['buildup_data'],
            config['washoff_data']
        )

        # Add pollutant sections to the SWMM file
        pollutant_manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, config['pollutants_data']['Name'])

    def test_modular_road_scenario(self):
        """Test modular road scenario builder with custom subcatchments."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'modular_road_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create road scenario for specific subcatchments with custom coverage
        subcatchments = ['S1']  # Adjust based on your model
        road_coverage = 50.0    # 50% road coverage
        pollutants = ['Zinc', 'Oil', 'Lead']  # Custom pollutant list

        manager = ModularPollutantBuilder.create_road_scenario(
            subcatchments, road_coverage, pollutants
        )

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, pollutants)

    def test_modular_urban_scenario(self):
        """Test modular urban scenario builder with custom land use mix."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'modular_urban_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create urban scenario with custom land use mix
        subcatchments = ['S1']
        land_use_mix = {
            'Residential': 70.0,
            'Commercial': 20.0,
            'Industrial': 10.0
        }
        pollutants = ['TSS', 'COD']  # Simplified pollutant list

        manager = ModularPollutantBuilder.create_urban_scenario(
            subcatchments, land_use_mix, pollutants
        )

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, pollutants)

    def test_modular_custom_scenario(self):
        """Test modular custom scenario builder with detailed per-subcatchment configuration."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'modular_custom_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create custom scenario with detailed configuration
        subcatchment_configs = [
            {
                'subcatchment_id': 'S1',
                'land_uses': {'Residential': 60.0, 'Road': 40.0},
                'pollutants': ['TSS', 'Zinc'],
                'initial_loadings': {'TSS': 150, 'Zinc': 80}
            }
        ]

        manager = ModularPollutantBuilder.create_custom_scenario(subcatchment_configs)

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        self._run_simulation_and_analyze(test_swmm_file, ['TSS', 'Zinc'])

    def test_builder_style_configuration(self):
        """Test the builder-style configuration interface."""
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'builder_style_test.inp')

        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)

        # Create configuration using builder-style interface
        manager = (SwmmPollutantManager()
                  .add_pollutant('TSS', units='MG/L', kdecay=0.0)
                  .add_pollutant('Copper', units='UG/L', kdecay=0.02)
                  .add_land_use('Mixed_Urban', sweeping_interval=5, fraction_available=0.2)
                  .assign_land_use_to_subcatchment('S1', 'Mixed_Urban', 100.0)
                  .set_initial_pollutant_loading('S1', 'TSS', 120)
                  .set_initial_pollutant_loading('S1', 'Copper', 50)
                  .add_buildup_function('Mixed_Urban', 'TSS', 'POW', 1.5, 0.7)
                  .add_buildup_function('Mixed_Urban', 'Copper', 'POW', 0.8, 0.5)
                  .add_washoff_function('Mixed_Urban', 'TSS', 'EXP', 0.02, 1.6, 0.6, 0.8)
                  .add_washoff_function('Mixed_Urban', 'Copper', 'EXP', 0.015, 1.4, 0.4, 0.6))

        # Add pollutant sections to the SWMM file
        manager.add_pollutant_sections_to_file(test_swmm_file)

        # Run the simulation and analyze results
        #self._run_simulation_and_analyze(test_swmm_file, ['TSS', 'Copper'])
        self._compute_outfall_load(test_swmm_file, 'O2', 'TSS')

    def _compute_outfall_load(self, output_file, outfall_node, pollutant_name):
        # Check if output file exists
        if not os.path.exists(output_file):
            raise FileNotFoundError(f"Output file not found: {output_file}")

        # Check file size (SWMM output files should be > 0 bytes)
        file_size = os.path.getsize(output_file)
        if file_size == 0:
            raise ValueError(f"Output file is empty: {output_file}")

        print(f"Reading output file: {output_file} (size: {file_size} bytes)")

        with Output(output_file) as out:
            if pollutant_name not in out.pollutants:
                raise ValueError(f"{pollutant_name} not found in output file pollutants")

            pollutant_index = out.pollutants[pollutant_name]
            times = out.times
            delta_t = (times[1] - times[0]).total_seconds() / 3600.0  # in hours

            # Debug: Check available attributes
            print(f"Available pollutants: {list(out.pollutants.keys())}")
            print(f"Number of time steps: {len(times)}")
            print(f"Time step interval: {delta_t} hours")

            # Get flow and concentration
            try:
                flow_series = out.node_series(outfall_node, 'FLOW')
                print(f"Flow series length: {len(flow_series)}")
            except Exception as e:
                print(f"Error getting flow series with 'FLOW': {e}")
                # Try alternative method with index
                try:
                    flow_series = out.node_series(outfall_node, 4)  # Flow is typically attribute 4
                    print(f"Flow series (by index 4) length: {len(flow_series)}")
                except Exception as e2:
                    print(f"Error getting flow series by index 4: {e2}")
                    raise ValueError(f"Could not retrieve flow data for node {outfall_node}")

            try:
                conc_series = out.node_series(outfall_node, pollutant_index)
                print(f"Concentration series length: {len(conc_series)}")
            except Exception as e:
                print(f"Error getting concentration series with index {pollutant_index}: {e}")
                # Try with base attributes offset
                try:
                    base_attrs = 6  # Common number of base node attributes
                    attr_index = base_attrs + pollutant_index
                    conc_series = out.node_series(outfall_node, attr_index)
                    print(f"Concentration series (by index {attr_index}) length: {len(conc_series)}")
                except Exception as e2:
                    print(f"Error getting concentration series by index {attr_index}: {e2}")
                    raise ValueError(f"Could not retrieve concentration data for pollutant {pollutant_name}")

            if len(flow_series) != len(conc_series):
                raise ValueError(f"Flow and concentration series mismatch: {len(flow_series)} vs {len(conc_series)}")

            # Handle different data types returned by node_series
            if isinstance(flow_series, dict):
                print(f"Flow series is a dict with keys: {list(flow_series.keys())}")
                # Try to extract the flow data from the dictionary
                if 'FLOW' in flow_series:
                    flow_data = flow_series['FLOW']
                elif len(flow_series) == 1:
                    flow_data = list(flow_series.values())[0]
                else:
                    raise ValueError(f"Cannot extract flow data from dict: {flow_series}")
            else:
                flow_data = flow_series

            if isinstance(conc_series, dict):
                print(f"Concentration series is a dict with keys: {list(conc_series.keys())}")
                # Try to extract the concentration data from the dictionary
                if pollutant_name in conc_series:
                    conc_data = conc_series[pollutant_name]
                elif len(conc_series) == 1:
                    conc_data = list(conc_series.values())[0]
                else:
                    raise ValueError(f"Cannot extract concentration data from dict: {conc_series}")
            else:
                conc_data = conc_series

            flow = np.array(flow_data)  # e.g., CMS
            conc = np.array(conc_data)  # e.g., mg/L

            print(f"Flow range: {np.min(flow):.6f} to {np.max(flow):.6f}")
            print(f"Concentration range: {np.min(conc):.6f} to {np.max(conc):.6f}")

            # Compute load in mass/time step (e.g., mg/hr)
            # Assumes concentration is in mg/L and flow is in CMS (cubic meters per second)
            # Convert CMS * hr -> m³
            volume_m3 = flow * 3600 * delta_t  # m³ per timestep
            mass_mg = conc * volume_m3 * 1000  # mg/L * m³ * 1000 L/m³ = mg

            total_load_kg = np.sum(mass_mg) / 1e6  # convert mg to kg

            return total_load_kg

    def _run_swmm_simulation(self, swmm_file_path: str):
        """
        Run SWMM simulation and ensure output file is created.

        Args:
            swmm_file_path: Path to the SWMM input file
        """
        # Change to output directory to run simulation
        original_dir = os.getcwd()
        output_dir = os.path.dirname(swmm_file_path)

        print(f"Running SWMM simulation...")
        print(f"  Input file: {swmm_file_path}")
        print(f"  Working directory: {output_dir}")
        print(f"  Original directory: {original_dir}")

        os.chdir(output_dir)

        try:
            # Run the simulation
            input_filename = os.path.basename(swmm_file_path)
            print(f"  Running simulation with file: {input_filename}")

            with Simulation(input_filename) as sim:
                for _ in sim:
                    pass

            # Check if output file was created in the current directory
            output_filename = os.path.splitext(input_filename)[0] + '.out'
            local_output_file = os.path.join(output_dir, output_filename)

            print(f"  Looking for output file: {local_output_file}")

            if not os.path.exists(local_output_file):
                # List files in the directory to see what was created
                files_in_dir = os.listdir(output_dir)
                print(f"  Files in output directory: {files_in_dir}")
                raise FileNotFoundError(f"SWMM simulation did not create output file: {local_output_file}")

            file_size = os.path.getsize(local_output_file)
            print(f"SWMM simulation completed successfully. Output file: {local_output_file} (size: {file_size} bytes)")

        except Exception as e:
            print(f"Error running SWMM simulation: {str(e)}")
            raise
        finally:
            # Change back to original directory
            os.chdir(original_dir)


if __name__ == '__main__':
    unittest.main()
