"""
Modular pollutant configuration builder for SWMM models.

This module provides easy-to-use functions for creating pollutant configurations
with flexible land use, pollutant, and subcatchment assignments.
"""

from typing import List, Dict, Any, Optional
from src.swmmgo.modifier.swmm_pollutant_manager import SwmmPollutantManager


class ModularPollutantBuilder:
    """Builder class for creating flexible pollutant configurations."""
    
    @staticmethod
    def create_road_scenario(subcatchment_ids: List[str], 
                           road_coverage_percent: float = 40.0,
                           pollutants: Optional[List[str]] = None) -> SwmmPollutantManager:
        """
        Create a road pollutant scenario for specified subcatchments.
        
        Args:
            subcatchment_ids: List of subcatchment IDs to apply road pollutants to
            road_coverage_percent: Percentage of each subcatchment that is road (default: 40%)
            pollutants: List of pollutants to include (default: ['Zinc', 'Oil'])
        
        Returns:
            Configured SwmmPollutantManager
        """
        if pollutants is None:
            pollutants = ['Zinc', 'Oil']
        
        manager = SwmmPollutantManager()
        
        # Add pollutants with road-specific parameters
        pollutant_configs = {
            'Zinc': {'units': 'MG/L', 'kdecay': 0.03},
            'Oil': {'units': 'MG/L', 'kdecay': 0.1}
        }
        
        # Add land use
        manager.add_land_use('Road', sweeping_interval=0, fraction_available=0.0)
        
        # Configure each pollutant
        for pollutant in pollutants:
            config = pollutant_configs.get(pollutant, {'units': 'MG/L', 'kdecay': 0.05})
            
            # Add pollutant
            manager.add_pollutant(pollutant, **config)
            
            # Add buildup and washoff functions for road
            if pollutant == 'Zinc':
                manager.add_buildup_function('Road', pollutant, 'POW', 0.6, 0.5, 0.0, 'AREA')
                manager.add_washoff_function('Road', pollutant, 'EXP', 0.01, 1.5, 0.0, 0.2)
            elif pollutant == 'Oil':
                manager.add_buildup_function('Road', pollutant, 'POW', 2.0, 0.6, 0.0, 'AREA')
                manager.add_washoff_function('Road', pollutant, 'EXP', 0.1, 1.0, 0.0, 0.4)
            else:
                # Default parameters for other pollutants
                manager.add_buildup_function('Road', pollutant, 'POW', 1.0, 0.6, 0.0, 'AREA')
                manager.add_washoff_function('Road', pollutant, 'EXP', 0.05, 1.2, 0.0, 0.3)
        
        # Configure subcatchments
        initial_loadings = {'Zinc': 100, 'Oil': 20}
        manager.configure_subcatchment_pollutants(
            subcatchment_ids,
            {'Road': road_coverage_percent},
            pollutants,
            initial_loadings
        )
        
        return manager
    
    @staticmethod
    def create_urban_scenario(subcatchment_ids: List[str],
                            land_use_mix: Optional[Dict[str, float]] = None,
                            pollutants: Optional[List[str]] = None) -> SwmmPollutantManager:
        """
        Create an urban pollutant scenario with mixed land uses.
        
        Args:
            subcatchment_ids: List of subcatchment IDs to configure
            land_use_mix: Dict mapping land use names to percentages (default: mixed residential/commercial)
            pollutants: List of pollutants to include (default: ['TSS', 'COD', 'TotalN', 'TotalP'])
        
        Returns:
            Configured SwmmPollutantManager
        """
        if land_use_mix is None:
            land_use_mix = {'Residential': 60.0, 'Commercial': 30.0, 'Industrial': 10.0}
        
        if pollutants is None:
            pollutants = ['TSS', 'COD', 'TotalN', 'TotalP']
        
        manager = SwmmPollutantManager()
        
        # Add pollutants with urban-specific parameters
        pollutant_configs = {
            'TSS': {'units': 'MG/L', 'kdecay': 0.0},
            'COD': {'units': 'MG/L', 'kdecay': 0.05, 'co_pollutant': 'TSS', 'co_frac': 0.3},
            'TotalN': {'units': 'MG/L', 'kdecay': 0.02, 'co_pollutant': 'TSS', 'co_frac': 0.1},
            'TotalP': {'units': 'MG/L', 'kdecay': 0.01, 'co_pollutant': 'TSS', 'co_frac': 0.05}
        }
        
        # Add land uses with different sweeping schedules
        land_use_configs = {
            'Residential': {'sweeping_interval': 7, 'fraction_available': 0.25},
            'Commercial': {'sweeping_interval': 3, 'fraction_available': 0.15},
            'Industrial': {'sweeping_interval': 1, 'fraction_available': 0.10}
        }
        
        for land_use, config in land_use_configs.items():
            if land_use in land_use_mix:
                manager.add_land_use(land_use, **config)
        
        # Configure pollutants and their relationships with land uses
        for pollutant in pollutants:
            config = pollutant_configs.get(pollutant, {'units': 'MG/L', 'kdecay': 0.0})
            manager.add_pollutant(pollutant, **config)
            
            # Add buildup and washoff for each land use
            for land_use in land_use_mix.keys():
                buildup_params = ModularPollutantBuilder._get_buildup_params(land_use, pollutant)
                washoff_params = ModularPollutantBuilder._get_washoff_params(land_use, pollutant)
                
                manager.add_buildup_function(land_use, pollutant, **buildup_params)
                manager.add_washoff_function(land_use, pollutant, **washoff_params)
        
        # Configure subcatchments
        initial_loadings = {'TSS': 200, 'COD': 150, 'TotalN': 50, 'TotalP': 10}
        manager.configure_subcatchment_pollutants(
            subcatchment_ids,
            land_use_mix,
            pollutants,
            initial_loadings
        )
        
        return manager
    
    @staticmethod
    def create_custom_scenario(subcatchment_configs: List[Dict[str, Any]]) -> SwmmPollutantManager:
        """
        Create a custom pollutant scenario with detailed per-subcatchment configuration.
        
        Args:
            subcatchment_configs: List of configuration dicts, each containing:
                - subcatchment_id: str
                - land_uses: Dict[str, float] (land use name -> percentage)
                - pollutants: List[str]
                - initial_loadings: Optional[Dict[str, float]]
                - custom_params: Optional[Dict] for custom pollutant/land use parameters
        
        Returns:
            Configured SwmmPollutantManager
        """
        manager = SwmmPollutantManager()
        
        # Collect all unique pollutants and land uses
        all_pollutants = set()
        all_land_uses = set()
        
        for config in subcatchment_configs:
            all_pollutants.update(config['pollutants'])
            all_land_uses.update(config['land_uses'].keys())
        
        # Add all pollutants and land uses with default parameters
        for pollutant in all_pollutants:
            manager.add_pollutant(pollutant)
        
        for land_use in all_land_uses:
            manager.add_land_use(land_use)
        
        # Add default buildup and washoff functions for all combinations
        for land_use in all_land_uses:
            for pollutant in all_pollutants:
                manager.add_buildup_function(land_use, pollutant)
                manager.add_washoff_function(land_use, pollutant)
        
        # Configure each subcatchment
        for config in subcatchment_configs:
            subcatchment_id = config['subcatchment_id']
            land_uses = config['land_uses']
            pollutants = config['pollutants']
            initial_loadings = config.get('initial_loadings', {})
            
            manager.configure_subcatchment_pollutants(
                [subcatchment_id],
                land_uses,
                pollutants,
                initial_loadings
            )
        
        return manager
    
    @staticmethod
    def _get_buildup_params(land_use: str, pollutant: str) -> Dict[str, Any]:
        """Get buildup parameters for land use and pollutant combination."""
        # Default parameters based on land use and pollutant type
        base_params = {'function': 'POW', 'coeff3': 0.0, 'per_unit': 'AREA'}
        
        if land_use == 'Residential':
            if pollutant == 'TSS':
                return {**base_params, 'coeff1': 1.2, 'coeff2': 0.7}
            elif pollutant in ['COD', 'TotalN', 'TotalP']:
                return {**base_params, 'coeff1': 0.8, 'coeff2': 0.6}
        elif land_use == 'Commercial':
            if pollutant == 'TSS':
                return {**base_params, 'coeff1': 2.0, 'coeff2': 0.8}
            elif pollutant in ['COD', 'TotalN', 'TotalP']:
                return {**base_params, 'coeff1': 1.5, 'coeff2': 0.7}
        elif land_use == 'Industrial':
            if pollutant == 'TSS':
                return {**base_params, 'coeff1': 3.0, 'coeff2': 0.9}
            elif pollutant in ['COD', 'TotalN', 'TotalP']:
                return {**base_params, 'coeff1': 2.5, 'coeff2': 0.8}
        
        # Default fallback
        return {**base_params, 'coeff1': 1.0, 'coeff2': 0.6}
    
    @staticmethod
    def _get_washoff_params(land_use: str, pollutant: str) -> Dict[str, Any]:
        """Get washoff parameters for land use and pollutant combination."""
        # Default parameters based on land use and pollutant type
        base_params = {'function': 'EXP'}
        
        if land_use == 'Residential':
            return {**base_params, 'coeff1': 0.012, 'coeff2': 1.8, 'sweep_rmvl': 0.7, 'bmp_rmvl': 0.8}
        elif land_use == 'Commercial':
            return {**base_params, 'coeff1': 0.020, 'coeff2': 2.0, 'sweep_rmvl': 0.8, 'bmp_rmvl': 0.85}
        elif land_use == 'Industrial':
            return {**base_params, 'coeff1': 0.030, 'coeff2': 2.2, 'sweep_rmvl': 0.9, 'bmp_rmvl': 0.9}
        
        # Default fallback
        return {**base_params, 'coeff1': 0.015, 'coeff2': 1.5, 'sweep_rmvl': 0.6, 'bmp_rmvl': 0.7}
