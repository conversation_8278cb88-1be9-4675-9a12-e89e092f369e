"""
Examples demonstrating the modular pollutant configuration system.

This script shows various ways to configure SWMM pollutants using the new modular approach.
"""

import os
import sys

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from swmmgo.modifier.swmm_pollutant_manager import SwmmPollutantManager
from swmmgo.modifier.modular_pollutant_builder import ModularPollutantBuilder


def example_1_simple_road_scenario():
    """Example 1: Simple road scenario for specific subcatchments."""
    print("=== Example 1: Simple Road Scenario ===")
    
    # Define which subcatchments to apply road pollutants to
    my_subcatchments = ['S1', 'S2', 'S3']
    
    # Create road scenario with 45% road coverage
    manager = ModularPollutantBuilder.create_road_scenario(
        subcatchment_ids=my_subcatchments,
        road_coverage_percent=45.0,
        pollutants=['Zinc', 'Oil', 'Lead']  # Custom pollutant list
    )
    
    # Apply to SWMM file
    # manager.add_pollutant_sections_to_file('my_model.inp')
    
    print(f"Configured road pollutants for subcatchments: {my_subcatchments}")
    print("Pollutants: Zinc, Oil, Lead with 45% road coverage")
    print()


def example_2_mixed_urban_scenario():
    """Example 2: Mixed urban scenario with custom land use distribution."""
    print("=== Example 2: Mixed Urban Scenario ===")
    
    # Define subcatchments and land use mix
    my_subcatchments = ['S1', 'S4', 'S5']
    land_use_distribution = {
        'Residential': 50.0,
        'Commercial': 30.0,
        'Industrial': 15.0,
        'Park': 5.0  # Custom land use
    }
    
    # Create urban scenario
    manager = ModularPollutantBuilder.create_urban_scenario(
        subcatchment_ids=my_subcatchments,
        land_use_mix=land_use_distribution,
        pollutants=['TSS', 'COD', 'TotalN']  # Subset of pollutants
    )
    
    # Apply to SWMM file
    # manager.add_pollutant_sections_to_file('my_urban_model.inp')
    
    print(f"Configured urban pollutants for subcatchments: {my_subcatchments}")
    print(f"Land use mix: {land_use_distribution}")
    print("Pollutants: TSS, COD, TotalN")
    print()


def example_3_custom_per_subcatchment():
    """Example 3: Custom configuration with different settings per subcatchment."""
    print("=== Example 3: Custom Per-Subcatchment Configuration ===")
    
    # Define detailed configuration for each subcatchment
    subcatchment_configs = [
        {
            'subcatchment_id': 'S1',  # Residential area
            'land_uses': {'Residential': 80.0, 'Road': 20.0},
            'pollutants': ['TSS', 'TotalN', 'TotalP'],
            'initial_loadings': {'TSS': 100, 'TotalN': 30, 'TotalP': 8}
        },
        {
            'subcatchment_id': 'S2',  # Commercial area
            'land_uses': {'Commercial': 70.0, 'Road': 30.0},
            'pollutants': ['TSS', 'COD', 'Oil'],
            'initial_loadings': {'TSS': 200, 'COD': 150, 'Oil': 25}
        },
        {
            'subcatchment_id': 'S3',  # Industrial area
            'land_uses': {'Industrial': 60.0, 'Road': 40.0},
            'pollutants': ['TSS', 'COD', 'Zinc', 'Lead'],
            'initial_loadings': {'TSS': 300, 'COD': 200, 'Zinc': 80, 'Lead': 40}
        }
    ]
    
    # Create custom scenario
    manager = ModularPollutantBuilder.create_custom_scenario(subcatchment_configs)
    
    # Apply to SWMM file
    # manager.add_pollutant_sections_to_file('my_custom_model.inp')
    
    print("Configured custom pollutant scenario:")
    for config in subcatchment_configs:
        print(f"  {config['subcatchment_id']}: {config['land_uses']} -> {config['pollutants']}")
    print()


def example_4_builder_style_interface():
    """Example 4: Using the builder-style interface for fine control."""
    print("=== Example 4: Builder-Style Interface ===")
    
    # Create configuration step by step using method chaining
    manager = (SwmmPollutantManager()
              # Add pollutants with specific parameters
              .add_pollutant('TSS', units='MG/L', kdecay=0.0)
              .add_pollutant('Copper', units='UG/L', kdecay=0.02)
              .add_pollutant('Phosphorus', units='MG/L', kdecay=0.01, co_pollutant='TSS', co_frac=0.05)
              
              # Add land uses with different sweeping schedules
              .add_land_use('High_Density_Residential', sweeping_interval=3, fraction_available=0.3)
              .add_land_use('Highway', sweeping_interval=1, fraction_available=0.1)
              
              # Assign land uses to subcatchments
              .assign_land_use_to_subcatchment('S1', 'High_Density_Residential', 70.0)
              .assign_land_use_to_subcatchment('S1', 'Highway', 30.0)
              .assign_land_use_to_subcatchment('S2', 'Highway', 100.0)
              
              # Set initial pollutant loadings
              .set_initial_pollutant_loading('S1', 'TSS', 150)
              .set_initial_pollutant_loading('S1', 'Copper', 60)
              .set_initial_pollutant_loading('S1', 'Phosphorus', 12)
              .set_initial_pollutant_loading('S2', 'TSS', 250)
              .set_initial_pollutant_loading('S2', 'Copper', 100)
              
              # Configure buildup functions
              .add_buildup_function('High_Density_Residential', 'TSS', 'POW', 1.8, 0.8)
              .add_buildup_function('High_Density_Residential', 'Copper', 'POW', 0.9, 0.6)
              .add_buildup_function('High_Density_Residential', 'Phosphorus', 'POW', 0.4, 0.5)
              .add_buildup_function('Highway', 'TSS', 'POW', 3.5, 0.9)
              .add_buildup_function('Highway', 'Copper', 'POW', 1.5, 0.7)
              
              # Configure washoff functions
              .add_washoff_function('High_Density_Residential', 'TSS', 'EXP', 0.018, 1.7, 0.7, 0.8)
              .add_washoff_function('High_Density_Residential', 'Copper', 'EXP', 0.012, 1.5, 0.5, 0.6)
              .add_washoff_function('High_Density_Residential', 'Phosphorus', 'EXP', 0.008, 1.3, 0.4, 0.5)
              .add_washoff_function('Highway', 'TSS', 'EXP', 0.035, 2.1, 0.9, 0.9)
              .add_washoff_function('Highway', 'Copper', 'EXP', 0.025, 1.8, 0.7, 0.8))
    
    # Apply to SWMM file
    # manager.add_pollutant_sections_to_file('my_detailed_model.inp')
    
    print("Configured detailed pollutant scenario using builder-style interface:")
    print("  - 3 pollutants: TSS, Copper, Phosphorus")
    print("  - 2 land uses: High_Density_Residential, Highway")
    print("  - 2 subcatchments: S1 (mixed), S2 (highway only)")
    print("  - Custom buildup and washoff parameters for each combination")
    print()


def example_5_adding_to_existing_configuration():
    """Example 5: Adding pollutants to an existing configuration."""
    print("=== Example 5: Adding to Existing Configuration ===")
    
    # Start with a road scenario
    manager = ModularPollutantBuilder.create_road_scenario(['S1'], 40.0)
    
    # Add additional pollutants and land uses
    manager.add_pollutant('Nitrogen', units='MG/L', kdecay=0.03)
    manager.add_land_use('Park', sweeping_interval=14, fraction_available=0.1)
    
    # Add the new combinations
    manager.assign_land_use_to_subcatchment('S1', 'Park', 20.0)  # 20% park, reducing road to 20%
    manager.set_initial_pollutant_loading('S1', 'Nitrogen', 40)
    manager.add_buildup_function('Park', 'Nitrogen', 'POW', 0.3, 0.4)
    manager.add_washoff_function('Park', 'Nitrogen', 'EXP', 0.005, 1.1, 0.2, 0.3)
    
    # Also add nitrogen to existing road land use
    manager.add_buildup_function('Road', 'Nitrogen', 'POW', 0.8, 0.6)
    manager.add_washoff_function('Road', 'Nitrogen', 'EXP', 0.015, 1.4, 0.1, 0.25)
    
    print("Extended road scenario with:")
    print("  - Added Nitrogen pollutant")
    print("  - Added Park land use (20% coverage)")
    print("  - Configured Nitrogen for both Road and Park land uses")
    print()


if __name__ == "__main__":
    print("Modular Pollutant Configuration Examples")
    print("=" * 50)
    print()
    
    example_1_simple_road_scenario()
    example_2_mixed_urban_scenario()
    example_3_custom_per_subcatchment()
    example_4_builder_style_interface()
    example_5_adding_to_existing_configuration()
    
    print("All examples completed!")
    print("\nTo use these configurations:")
    print("1. Uncomment the .add_pollutant_sections_to_file() calls")
    print("2. Provide the path to your SWMM .inp file")
    print("3. Run the script to modify your SWMM model")
