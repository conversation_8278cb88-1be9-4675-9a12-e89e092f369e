"""
Predefined pollutant configurations for SWMM models.

This module contains commonly used pollutant configurations that can be easily
imported and used with the SwmmPollutantManager class.
"""

from typing import Dict, List, Any


class PollutantConfigurations:
    """Collection of predefined pollutant configurations."""
    
    @staticmethod
    def get_road_pollutants_zinc_oil() -> Dict[str, Dict[str, List[Any]]]:
        """
        Get a configuration for road pollutants focusing on Zinc and Oil.
        
        Returns:
            Dictionary containing all pollutant-related data for road scenarios
        """
        return {
            'pollutants_data': {
                'Name': ['Zinc', 'Oil'],
                'Units': ['MG/L', 'MG/L'],
                'Crain': [0.0, 0.0],
                'Cgw': [0.0, 0.0],
                'Crdii': [0, 0],
                'Kdecay': [0.03, 0.1],
                'SnowOnly': ['NO', 'NO'],
                'Co-Pollutant': ['*', '*'],
                'Co-Frac': [0.0, 0.0],
                'Cdwf': [0, 0],
                'Cinit': [0, 0]
            },
            'landuses_data': {
                'Name': ['Residential_road'],
                'Sweeping_Interval': [0],
                'Fraction_Available': [0],
                'Last_Swept': [0]
            },
            'coverages_data': {
                'Subcatchment': ['S1'],
                'Land_Use': ['Residential_road'],
                'Percent': [40]
            },
            'loadings_data': {
                'Subcatchment': ['S1', 'S1'],
                'Pollutant': ['Zinc', 'Oil'],
                'Buildup': [100, 20]
            },
            'buildup_data': {
                'Land_Use': ['Residential_road', 'Residential_road'],
                'Pollutant': ['Zinc', 'Oil'],
                'Function': ['POW', 'POW'],
                'Coeff1': [0.6, 2.0],
                'Coeff2': [0.5, 0.6],
                'Coeff3': [0, 0.0],
                'Per_Unit': ['AREA', 'AREA']
            },
            'washoff_data': {
                'Land_Use': ['Residential_road', 'Residential_road'],
                'Pollutant': ['Zinc', 'Oil'],
                'Function': ['EXP', 'EXP'],
                'Coeff1': [0.01, 0.1],
                'Coeff2': [1.5, 1.0],
                'SweepRmvl': [0.0, 0.0],
                'BmpRmvl': [0.2, 0.4]
            }
        }
    
    @staticmethod
    def get_urban_pollutants_comprehensive() -> Dict[str, Dict[str, List[Any]]]:
        """
        Get a comprehensive urban pollutant configuration including TSS, COD, and nutrients.
        
        Returns:
            Dictionary containing all pollutant-related data for urban scenarios
        """
        return {
            'pollutants_data': {
                'Name': ['TSS', 'COD', 'TotalN', 'TotalP'],
                'Units': ['MG/L', 'MG/L', 'MG/L', 'MG/L'],
                'Crain': [0.0, 0.0, 0.0, 0.0],
                'Cgw': [0.0, 0.0, 0.0, 0.0],
                'Crdii': [0, 0, 0, 0],
                'Kdecay': [0.0, 0.05, 0.02, 0.01],
                'SnowOnly': ['NO', 'NO', 'NO', 'NO'],
                'Co-Pollutant': ['*', 'TSS', 'TSS', 'TSS'],
                'Co-Frac': [0.0, 0.3, 0.1, 0.05],
                'Cdwf': [0, 0, 0, 0],
                'Cinit': [0, 0, 0, 0]
            },
            'landuses_data': {
                'Name': ['Residential', 'Commercial', 'Industrial'],
                'Sweeping_Interval': [7, 3, 1],
                'Fraction_Available': [0.25, 0.15, 0.10],
                'Last_Swept': [0, 0, 0]
            },
            'coverages_data': {
                'Subcatchment': ['S1', 'S1', 'S1'],
                'Land_Use': ['Residential', 'Commercial', 'Industrial'],
                'Percent': [60, 30, 10]
            },
            'loadings_data': {
                'Subcatchment': ['S1', 'S1', 'S1', 'S1'],
                'Pollutant': ['TSS', 'COD', 'TotalN', 'TotalP'],
                'Buildup': [200, 150, 50, 10]
            },
            'buildup_data': {
                'Land_Use': ['Residential', 'Residential', 'Residential', 'Residential',
                           'Commercial', 'Commercial', 'Commercial', 'Commercial',
                           'Industrial', 'Industrial', 'Industrial', 'Industrial'],
                'Pollutant': ['TSS', 'COD', 'TotalN', 'TotalP',
                            'TSS', 'COD', 'TotalN', 'TotalP',
                            'TSS', 'COD', 'TotalN', 'TotalP'],
                'Function': ['POW', 'POW', 'POW', 'POW',
                           'POW', 'POW', 'POW', 'POW',
                           'POW', 'POW', 'POW', 'POW'],
                'Coeff1': [1.2, 0.8, 0.3, 0.1,
                          2.0, 1.5, 0.5, 0.15,
                          3.0, 2.5, 0.8, 0.25],
                'Coeff2': [0.7, 0.6, 0.5, 0.4,
                          0.8, 0.7, 0.6, 0.5,
                          0.9, 0.8, 0.7, 0.6],
                'Coeff3': [0, 0, 0, 0,
                          0, 0, 0, 0,
                          0, 0, 0, 0],
                'Per_Unit': ['AREA', 'AREA', 'AREA', 'AREA',
                           'AREA', 'AREA', 'AREA', 'AREA',
                           'AREA', 'AREA', 'AREA', 'AREA']
            },
            'washoff_data': {
                'Land_Use': ['Residential', 'Residential', 'Residential', 'Residential',
                           'Commercial', 'Commercial', 'Commercial', 'Commercial',
                           'Industrial', 'Industrial', 'Industrial', 'Industrial'],
                'Pollutant': ['TSS', 'COD', 'TotalN', 'TotalP',
                            'TSS', 'COD', 'TotalN', 'TotalP',
                            'TSS', 'COD', 'TotalN', 'TotalP'],
                'Function': ['EXP', 'EXP', 'EXP', 'EXP',
                           'EXP', 'EXP', 'EXP', 'EXP',
                           'EXP', 'EXP', 'EXP', 'EXP'],
                'Coeff1': [0.012, 0.008, 0.005, 0.003,
                          0.020, 0.015, 0.010, 0.006,
                          0.030, 0.025, 0.018, 0.012],
                'Coeff2': [1.8, 1.5, 1.2, 1.0,
                          2.0, 1.8, 1.5, 1.2,
                          2.2, 2.0, 1.8, 1.5],
                'SweepRmvl': [0.7, 0.5, 0.3, 0.2,
                             0.8, 0.6, 0.4, 0.3,
                             0.9, 0.7, 0.5, 0.4],
                'BmpRmvl': [0.8, 0.6, 0.4, 0.3,
                           0.85, 0.65, 0.45, 0.35,
                           0.9, 0.7, 0.5, 0.4]
            }
        }
    
    @staticmethod
    def get_simple_tss_configuration() -> Dict[str, Dict[str, List[Any]]]:
        """
        Get a simple TSS (Total Suspended Solids) only configuration for basic testing.
        
        Returns:
            Dictionary containing TSS-only pollutant data
        """
        return {
            'pollutants_data': {
                'Name': ['TSS'],
                'Units': ['MG/L'],
                'Crain': [0.0],
                'Cgw': [0.0],
                'Crdii': [0],
                'Kdecay': [0.0],
                'SnowOnly': ['NO'],
                'Co-Pollutant': ['*'],
                'Co-Frac': [0.0],
                'Cdwf': [0],
                'Cinit': [0]
            },
            'landuses_data': {
                'Name': ['Urban'],
                'Sweeping_Interval': [7],
                'Fraction_Available': [0.25],
                'Last_Swept': [0]
            },
            'coverages_data': {
                'Subcatchment': ['S1'],
                'Land_Use': ['Urban'],
                'Percent': [100]
            },
            'loadings_data': {
                'Subcatchment': ['S1'],
                'Pollutant': ['TSS'],
                'Buildup': [150]
            },
            'buildup_data': {
                'Land_Use': ['Urban'],
                'Pollutant': ['TSS'],
                'Function': ['POW'],
                'Coeff1': [1.0],
                'Coeff2': [0.6],
                'Coeff3': [0],
                'Per_Unit': ['AREA']
            },
            'washoff_data': {
                'Land_Use': ['Urban'],
                'Pollutant': ['TSS'],
                'Function': ['EXP'],
                'Coeff1': [0.015],
                'Coeff2': [1.5],
                'SweepRmvl': [0.6],
                'BmpRmvl': [0.7]
            }
        }
